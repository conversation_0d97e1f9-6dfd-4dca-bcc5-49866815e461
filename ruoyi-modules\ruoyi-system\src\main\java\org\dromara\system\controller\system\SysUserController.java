package org.dromara.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.crypto.digest.BCrypt;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import io.github.linpeilie.BaseMapper;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.constant.TenantConstants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.dto.StartProcessDTO;
import org.dromara.common.core.domain.dto.StartProcessReturnDTO;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.enums.BusinessStatusEnum;
import org.dromara.common.core.service.WorkflowService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.encrypt.annotation.ApiEncrypt;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.sse.core.SseEmitterManager;
import org.dromara.common.sse.dto.SseMessageDto;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.SysUserRole;
import org.dromara.system.domain.bo.SysDeptBo;
import org.dromara.system.domain.bo.SysPostBo;
import org.dromara.system.domain.bo.SysRoleBo;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.*;
import org.dromara.system.listener.SysUserImportListener;
import org.dromara.system.service.*;
import org.dromara.system.domain.vo.SysUserApprovalVo;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {

    private final ISysUserService userService;
    private final ISysRoleService roleService;
    private final ISysPostService postService;
    private final ISysDeptService deptService;
    private final ISysTenantService tenantService;
    private final WorkflowService workflowService;
//    private final BaseMapper baseMapper;

    private final SseEmitterManager sseEmitterManager;

    private final ISysUserApprovalService userApprovalService;

    /**
     * 获取用户列表
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/list")
//    @RequestParam(required = false)
    public TableDataInfo<SysUserVo> list(SysUserBo user, PageQuery pageQuery, String deptParam) {
        if (StringUtils.isNotEmpty(deptParam) &&
            (deptParam.equals("company") || deptParam.equals("department") || deptParam.equals("unit"))) {
            if (deptParam.equals("company")) {
                return userService.selectPageUserList(user, pageQuery);
            }
            return userService.selectPageUserList(user, pageQuery, deptParam);
        } else {
            return userService.selectPageUserList(user, pageQuery);
        }
    }

    /**
     * 导出用户列表
     */
    @Log(
        title = "用户管理",
        businessType = BusinessType.EXPORT,
        tableEntity = SysUser.class
        )
    @SaCheckPermission("system:user:export")
    @PostMapping("/export")
    public void export(SysUserBo user, HttpServletResponse response) {
        List<SysUserExportVo> list = userService.selectUserExportList(user);
        ExcelUtil.exportExcel(list, "用户数据", SysUserExportVo.class, response);
    }

    /**
     * 导入数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新已存在数据
     */
    @Log(
        title = "用户管理",
        businessType = BusinessType.IMPORT,
        tableEntity = SysUser.class
        )
    @SaCheckPermission("system:user:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        ExcelResult<SysUserImportVo> result = ExcelUtil.importExcel(file.getInputStream(), SysUserImportVo.class, new SysUserImportListener(updateSupport));
        return R.ok(result.getAnalysis());
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "用户数据", SysUserImportVo.class, response);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public R<UserInfoVo> getInfo() {
        UserInfoVo userInfoVo = new UserInfoVo();
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (TenantHelper.isEnable() && LoginHelper.isSuperAdmin() || LoginHelper.isSystemAdmin()) {
            // 超级管理员 如果重新加载用户信息需清除动态租户
            TenantHelper.clearDynamic();
        }
        SysUserVo user = userService.selectUserById(loginUser.getUserId());
        if (ObjectUtil.isNull(user)) {
            return R.fail("没有权限访问用户数据!");
        }
        userInfoVo.setUser(user);
        userInfoVo.setPermissions(loginUser.getMenuPermission());
        userInfoVo.setRoles(loginUser.getRolePermission());
        return R.ok(userInfoVo);
    }

    /**
     * 根据用户编号获取详细信息
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:user:query")
    @GetMapping(value = {"/", "/{userId}"})
    public R<SysUserInfoVo> getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        SysUserInfoVo userInfoVo = new SysUserInfoVo();
        if (ObjectUtil.isNotNull(userId)) {
            userService.checkUserDataScope(userId);

//            SysUserVo sysUser = userService.selectUserById(userId);
            // 对于待审批用户，可能已被逻辑删除，需要特殊处理(注释上一行原始过滤del数据的查询方式)
            SysUserVo sysUser = userService.selectUserByIdIncludeDeleted(userId);
            if (sysUser == null) {
                return R.fail("未找到指定用户");
            }

            userInfoVo.setUser(sysUser);
            userInfoVo.setRoleIds(roleService.selectRoleListByUserId(userId));
            Long deptId = sysUser.getDeptId();
            if (ObjectUtil.isNotNull(deptId)) {
                SysPostBo postBo = new SysPostBo();
                postBo.setDeptId(deptId);
                userInfoVo.setPosts(postService.selectPostList(postBo));
                userInfoVo.setPostIds(postService.selectPostListByUserId(userId));
            }
        }
        SysRoleBo roleBo = new SysRoleBo();
        roleBo.setStatus(SystemConstants.NORMAL);
        List<SysRoleVo> roles = roleService.selectRoleList(roleBo);
        userInfoVo.setRoles(LoginHelper.isSuperAdmin(userId) ? roles : StreamUtils.filter(roles, r -> !r.isSuperAdmin()));
        return R.ok(userInfoVo);
    }

    /**
     * 新增用户
     */
    @SaCheckPermission("system:user:add")
    @Log(
        title = "用户管理",
        businessType = BusinessType.INSERT,
        requestObjSpel = "#user",
        tableEntity = SysUser.class
    )
    @PostMapping
    public R<SysUserVo> add(@Validated @RequestBody SysUserBo user) {
        deptService.checkDeptDataScope(user.getDeptId());

        // 检查当前用户是否为系统管理员
        if (LoginHelper.isSystemAdmin()) {
            // 系统管理员新增用户需要走审批流程
            if (!userService.checkUserNameUnique(user)) {
                return R.fail("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
            } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
                return R.fail("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
            } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
                return R.fail("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
            }
            if (TenantHelper.isEnable()) {
                if (!tenantService.checkAccountBalance(TenantHelper.getTenantId())) {
                    return R.fail("当前租户下用户名额不足，请联系管理员");
                }
            }

            // 创建审批申请记录
            String approvalData = JsonUtils.toJsonString(user);
            SysUserApprovalVo approval = userApprovalService.createApproval(null, "add", approvalData);

            // 启动用户信息新增流程
            StartProcessDTO startProcess = new StartProcessDTO();
            // 使用审批记录ID作为businessId
            startProcess.setBusinessId(approval.getApprovalId().toString());
            startProcess.setFlowCode("userUpdate");

            // 将操作类型和用户数据作为流程变量传递
            Map<String, Object> variables = new HashMap<>();
            variables.put("operationType", "add");
            variables.put("userData", approvalData);
            startProcess.setVariables(variables);

            StartProcessReturnDTO processResult = workflowService.startWorkFlow(startProcess);

            if (processResult != null) {
                // 更新审批记录的流程实例ID
                userApprovalService.updateApprovalStatus(approval.getApprovalId(),
                    BusinessStatusEnum.WAITING.getStatus(), processResult.getProcessInstanceId());
                return R.ok("用户新增申请已提交审批");
            } else {
                return R.fail("提交审批失败");
            }

//            user.setPassword(BCrypt.hashpw(user.getPassword()));
//            user.setStatus(SystemConstants.DISABLE);
//            user.setFlowStatus(BusinessStatusEnum.WAITING.getStatus());
//
//            SseMessageDto dto = new SseMessageDto();
//            List<Long> userIds = new ArrayList<>();
//            userIds.add(3L);
//            dto.setUserIds(userIds);
//            dto.setMessage(String.format("新增用户%s(%s)，请尽快审批", user.getNickName(),  user.getUserName()));
//            sseEmitterManager.publishMessage(dto);

//            return R.ok(userService.insertUser(user));
        } else {
            return R.fail("只有系统管理员才能新增用户");
        }
    }

    /**
     * 修改用户
     */
    @SaCheckPermission("system:user:edit")
    @Log(
        title = "用户管理",
        businessType = BusinessType.UPDATE,
        requestObjSpel = "#user",
        operatorObjIdSpel = "#user.getUserId()",
        tableEntity = SysUser.class
    )
    @PutMapping
    public R<SysUserVo> edit(@Validated @RequestBody SysUserBo user) {
        userService.checkUserAllowed(user.getUserId());
        userService.checkUserDataScope(user.getUserId());
        deptService.checkDeptDataScope(user.getDeptId());

        // 检查当前用户是否为系统管理员
        if (LoginHelper.isSystemAdmin()) {
            // 系统管理员修改用户信息需要走审批流程
            // 获取原始用户信息
            SysUserVo originalUser = userService.selectUserById(user.getUserId());

            // 计算用户信息差异（包括基本信息、角色和岗位）
            Map<String, Object> userInfoDiff = userService.computeDiff(originalUser, user);

            // 如果没有差异，直接返回成功
            if (userInfoDiff.isEmpty()) {
                return R.ok("用户信息无变化");
            }

            // 创建审批申请记录
            String approvalData = JsonUtils.toJsonString(userInfoDiff);
            SysUserApprovalVo approval = userApprovalService.createApproval(user.getUserId(), "edit", approvalData);

            // 启动用户信息修改流程
            StartProcessDTO startProcess = new StartProcessDTO();
            startProcess.setBusinessId(approval.getApprovalId().toString());
            startProcess.setFlowCode("userUpdate");

            // 将用户信息差异和操作类型作为流程变量传递
            Map<String, Object> variables = new HashMap<>();
            variables.put("userInfo", userInfoDiff);
            variables.put("operationType", "edit");
            variables.put("userId", user.getUserId());
            startProcess.setVariables(variables);

            StartProcessReturnDTO processResult = workflowService.startWorkFlow(startProcess);

            if (processResult != null) {
                // 更新审批记录的流程实例ID
                userApprovalService.updateApprovalStatus(approval.getApprovalId(),
                    BusinessStatusEnum.WAITING.getStatus(), processResult.getProcessInstanceId());
                return R.ok("用户信息修改已提交审批");
            } else {
                return R.fail("提交审批失败");
            }

//            // 字段方式
//            Map<String, Object> diff = userService.computeDiff(userService.selectUserById(user.getUserId()), user);
//            String diffMessage = diff.entrySet().stream()
//                .map(entry -> entry.getKey() + ":" + entry.getValue())
//                .collect(Collectors.joining(", "));
//            user.setStatus(SystemConstants.DISABLE);
//            user.setFlowStatus(BusinessStatusEnum.WAITING.getStatus());
//
//
//
//            SseMessageDto dto = new SseMessageDto();
//            List<Long> userIds = new ArrayList<>();
//            userIds.add(3L);
//            dto.setUserIds(userIds);
//            dto.setMessage(String.format(
//                "修改用户%s(%s)，变更字段：%s，请尽快审批",
//                user.getNickName(),
//                user.getUserName(),
//                diffMessage
//            ));
//            sseEmitterManager.publishMessage(dto);

//            return R.ok(userService.updateUser(user));
        } else if (LoginHelper.isRightAdmin()) {
            // 安全管理员可以直接修改用户信息
            if (!userService.checkUserNameUnique(user)) {
                return R.fail("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
            } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
                return R.fail("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
            } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
                return R.fail("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
            }

//            user.setFlowStatus(BusinessStatusEnum.FINISH.getStatus());
            return R.ok(userService.updateUser(user));
        } else {
            return R.fail("没有权限修改用户信息");
        }
    }

    /**
     * 删除用户
     *
     * @param userIds 角色ID串
     */
    @SaCheckPermission("system:user:remove")
    @Log(
        title = "用户管理",
        businessType = BusinessType.DELETE,
        operatorObjIdSpel = "#userIds",
        tableEntity = SysUser.class
    )
    @DeleteMapping("/{userIds}")
    public R<Void> remove(@PathVariable Long[] userIds) {
        if (ArrayUtil.contains(userIds, LoginHelper.getUserId())) {
            return R.fail("当前用户不能删除");
        }

//        if (LoginHelper.isSystemAdmin()) {
//            for(Long userId : userIds){
//                SysUserVo user= userService.selectUserById(userId);
//                user.setStatus(SystemConstants.DISABLE);
//                user.setFlowStatus(BusinessStatusEnum.INVALID.getStatus());
//                userService.updateUser(MapstructUtils.convert(user, SysUserBo.class));
//
//                SseMessageDto dto = new SseMessageDto();
//                List<Long> tmp = new ArrayList<>();
//                tmp.add(3L);
//                dto.setUserIds(tmp);
//                dto.setMessage(String.format("删除用户%s(%s)，请尽快审批", user.getNickName(),  user.getUserName()));
//                sseEmitterManager.publishMessage(dto);
//            }
//        } else if (LoginHelper.isRightAdmin()) {
//            return toAjax(userService.deleteUserByIds(userIds));
//        }

        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 根据用户ID串批量获取用户基础信息
     *
     * @param userIds 用户ID串
     * @param deptId  部门ID
     */
    @SaCheckPermission("system:user:query")
    @GetMapping("/optionselect")
    public R<List<SysUserVo>> optionselect(@RequestParam(required = false) Long[] userIds,
                                           @RequestParam(required = false) Long deptId) {
        return R.ok(userService.selectUserByIds(ArrayUtil.isEmpty(userIds) ? null : List.of(userIds), deptId));
    }

    /**
     * 重置密码
     */
    @ApiEncrypt
    @SaCheckPermission("system:user:resetPwd")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public R<Void> resetPwd(@RequestBody SysUserBo user) {
        userService.checkUserAllowed(user.getUserId());
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(BCrypt.hashpw(user.getPassword()));
        return toAjax(userService.resetUserPwd(user.getUserId(), user.getPassword()));
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysUserBo user) {
        userService.checkUserAllowed(user.getUserId());
        userService.checkUserDataScope(user.getUserId());
        return toAjax(userService.updateUserStatus(user.getUserId(), user.getStatus()));
    }

    /**
     * 根据用户编号获取授权角色
     *
     * @param userId 用户ID
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY,
        TenantConstants.RIGHT_ROLE_KEY,
        TenantConstants.OPERATOR_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:user:query")
    @GetMapping("/authRole/{userId}")
    public R<SysUserInfoVo> authRole(@PathVariable Long userId) {
        userService.checkUserDataScope(userId);
        SysUserVo user = userService.selectUserById(userId);
        List<SysRoleVo> roles = roleService.selectRolesAuthByUserId(userId);
        SysUserInfoVo userInfoVo = new SysUserInfoVo();
        userInfoVo.setUser(user);
        // 组合条件过滤(给用户分角色的时候无法选中三员角色)
        userInfoVo.setRoles(LoginHelper.isSuperAdmin(userId) ?
            roles :
            StreamUtils.filter(roles, r ->
                !r.isSuperAdmin() &&
                    !r.isSystemAdmin() &&
                    !r.isRightAdmin() &&
                    !r.isCheckAdmin()
            )
        );
        return R.ok(userInfoVo);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户Id
     * @param roleIds 角色ID串
     */
    @SaCheckPermission("system:user:edit")
    @Log(
        title = "用户管理",
        businessType = BusinessType.GRANT,
        operatorObjIdSpel = "#userId",
        requestObjSpel = "#roleIds",
        tableEntity = SysUserRole.class
    )
    @PutMapping("/authRole")
    public R<Void> insertAuthRole(Long userId, Long[] roleIds) {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds);
        return R.ok();
    }

    /**
     * 获取部门树列表
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/deptTree")
    public R<List<Tree<Long>>> deptTree(SysDeptBo dept, @RequestParam(required = false) String deptParam) {
        if (StringUtils.isNotEmpty(deptParam) &&
            (deptParam.equals("company") || deptParam.equals("department") || deptParam.equals("unit"))) {
            return R.ok(deptService.selectDeptTreeList(dept, deptParam));
        } else {
            return R.ok(deptService.selectDeptTreeList(dept));
        }
    }

    /**
     * 获取部门下的所有用户信息
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/list/dept/{deptId}")
    public R<List<SysUserVo>> listByDept(@PathVariable @NotNull Long deptId) {
        return R.ok(userService.selectUserListByDept(deptId));
    }

}
