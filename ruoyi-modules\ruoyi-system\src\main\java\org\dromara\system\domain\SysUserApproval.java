package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 用户审批申请对象 sys_user_approval
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_approval")
public class SysUserApproval extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审批ID
     */
    @TableId(value = "approval_id")
    private Long approvalId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 审批类型（add新增 edit修改 delete删除）
     */
    private String approvalType;

    /**
     * 审批数据（JSON格式）
     */
    private String approvalData;

    /**
     * 审批状态
     */
    private String approvalStatus;

    /**
     * 流程实例ID
     */
    private Long flowInstanceId;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;
}
