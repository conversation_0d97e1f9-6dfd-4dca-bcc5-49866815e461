package org.dromara.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.enums.BusinessStatusEnum;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.SysUserApproval;
import org.dromara.system.domain.bo.SysUserApprovalBo;
import org.dromara.system.domain.vo.SysUserApprovalVo;
import org.dromara.system.mapper.SysUserApprovalMapper;
import org.dromara.system.service.ISysUserApprovalService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 用户审批申请Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class SysUserApprovalServiceImpl implements ISysUserApprovalService {

    private final SysUserApprovalMapper baseMapper;

    /**
     * 查询用户审批申请
     */
    @Override
    public SysUserApprovalVo queryById(Long approvalId) {
        return baseMapper.selectVoById(approvalId);
    }

    /**
     * 查询用户审批申请列表
     */
    @Override
    public TableDataInfo<SysUserApprovalVo> queryPageList(SysUserApprovalBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysUserApproval> lqw = buildQueryWrapper(bo);
        Page<SysUserApprovalVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询用户审批申请列表
     */
    @Override
    public List<SysUserApprovalVo> queryList(SysUserApprovalBo bo) {
        LambdaQueryWrapper<SysUserApproval> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysUserApproval> buildQueryWrapper(SysUserApprovalBo bo) {
        LambdaQueryWrapper<SysUserApproval> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SysUserApproval::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getApprovalType()), SysUserApproval::getApprovalType, bo.getApprovalType());
        lqw.eq(StringUtils.isNotBlank(bo.getApprovalStatus()), SysUserApproval::getApprovalStatus, bo.getApprovalStatus());
        lqw.eq(bo.getFlowInstanceId() != null, SysUserApproval::getFlowInstanceId, bo.getFlowInstanceId());
        return lqw;
    }

    /**
     * 新增用户审批申请
     */
    @Override
    public Boolean insertByBo(SysUserApprovalBo bo) {
        SysUserApproval add = MapstructUtils.convert(bo, SysUserApproval.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setApprovalId(add.getApprovalId());
        }
        return flag;
    }

    /**
     * 修改用户审批申请
     */
    @Override
    public Boolean updateByBo(SysUserApprovalBo bo) {
        SysUserApproval update = MapstructUtils.convert(bo, SysUserApproval.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysUserApproval entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除用户审批申请
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 创建用户审批申请记录
     */
    @Override
    public SysUserApprovalVo createApproval(Long userId, String approvalType, String approvalData) {
        SysUserApprovalBo bo = new SysUserApprovalBo();
        bo.setUserId(userId);
        bo.setApprovalType(approvalType);
        bo.setApprovalData(approvalData);
        bo.setApprovalStatus(BusinessStatusEnum.DRAFT.getStatus());
        
        insertByBo(bo);
        return queryById(bo.getApprovalId());
    }

    /**
     * 更新审批状态
     */
    @Override
    public void updateApprovalStatus(Long approvalId, String status, Long flowInstanceId) {
        LambdaUpdateWrapper<SysUserApproval> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(SysUserApproval::getApprovalStatus, status)
               .set(flowInstanceId != null, SysUserApproval::getFlowInstanceId, flowInstanceId)
               .eq(SysUserApproval::getApprovalId, approvalId);
        baseMapper.update(wrapper);
    }

    /**
     * 根据审批ID查询用户ID
     */
    @Override
    public Long getUserIdByApprovalId(Long approvalId) {
        SysUserApprovalVo approval = queryById(approvalId);
        return approval != null ? approval.getUserId() : null;
    }
}
