package org.dromara.system.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.SysUserApprovalBo;
import org.dromara.system.domain.vo.SysUserApprovalVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户审批申请Service接口
 *
 * <AUTHOR>
 */
public interface ISysUserApprovalService {

    /**
     * 查询用户审批申请
     */
    SysUserApprovalVo queryById(Long approvalId);

    /**
     * 查询用户审批申请列表
     */
    TableDataInfo<SysUserApprovalVo> queryPageList(SysUserApprovalBo bo, PageQuery pageQuery);

    /**
     * 查询用户审批申请列表
     */
    List<SysUserApprovalVo> queryList(SysUserApprovalBo bo);

    /**
     * 新增用户审批申请
     */
    Boolean insertByBo(SysUserApprovalBo bo);

    /**
     * 修改用户审批申请
     */
    Boolean updateByBo(SysUserApprovalBo bo);

    /**
     * 校验并批量删除用户审批申请信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 创建用户审批申请记录
     *
     * @param userId 用户ID
     * @param approvalType 审批类型
     * @param approvalData 审批数据
     * @return 审批记录
     */
    SysUserApprovalVo createApproval(Long userId, String approvalType, String approvalData);

    /**
     * 更新审批状态
     *
     * @param approvalId 审批ID
     * @param status 状态
     * @param flowInstanceId 流程实例ID
     */
    void updateApprovalStatus(Long approvalId, String status, Long flowInstanceId);

    /**
     * 根据审批ID查询用户ID
     *
     * @param approvalId 审批ID
     * @return 用户ID
     */
    Long getUserIdByApprovalId(Long approvalId);
}
