-- ----------------------------
-- 用户审批申请表
-- ----------------------------
CREATE TABLE `sys_user_approval` (
    `approval_id`     BIGINT          NOT NULL                COMMENT '审批ID',
    `tenant_id`       VARCHAR(20)     DEFAULT '000000'        COMMENT '租户编号',
    `user_id`         BIGINT          NULL                    COMMENT '用户ID（新增时为空，修改删除时有值）',
    `approval_type`   VARCHAR(20)     NOT NULL                COMMENT '审批类型（add新增 edit修改 delete删除）',
    `approval_data`   LONGTEXT                                COMMENT '审批数据（JSON格式）',
    `approval_status` VARCHAR(20)     DEFAULT 'draft'         COMMENT '审批状态',
    `flow_instance_id` BIGINT                                 COMMENT '流程实例ID',
    `del_flag`        CHAR(1)         DEFAULT '0'             COMMENT '删除标志（0代表存在 1代表删除）',
    `create_dept`     BIGINT          NULL                    COMMENT '创建部门',
    `create_by`       BIGINT          NULL                    COMMENT '创建者',
    `create_time`     DATETIME        NULL                    COMMENT '创建时间',
    `update_by`       BIGINT          NULL                    COMMENT '更新者',
    `update_time`     DATETIME        NULL                    COMMENT '更新时间',
    `remark`          VARCHAR(500)    NULL                    COMMENT '备注',
    PRIMARY KEY (`approval_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户审批申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 添加索引
-- ----------------------------
CREATE INDEX `idx_sys_user_approval_user_id` ON `sys_user_approval`(`user_id`) USING BTREE;
CREATE INDEX `idx_sys_user_approval_type` ON `sys_user_approval`(`approval_type`) USING BTREE;
CREATE INDEX `idx_sys_user_approval_status` ON `sys_user_approval`(`approval_status`) USING BTREE;
CREATE INDEX `idx_sys_user_approval_flow_instance` ON `sys_user_approval`(`flow_instance_id`) USING BTREE;
CREATE INDEX `idx_sys_user_approval_tenant` ON `sys_user_approval`(`tenant_id`) USING BTREE;

-- ----------------------------
-- 添加字典数据
-- ----------------------------
INSERT INTO sys_dict_type VALUES (NULL, '000000', '用户审批类型', 'sys_user_approval_type', 0, 103, 1, NOW(), NULL, NULL, '用户审批类型列表');

INSERT INTO sys_dict_data VALUES (NULL, '000000', 1, '新增', 'add', 'sys_user_approval_type', '', 'primary', 'N', 103, 1, NOW(), NULL, NULL, '新增用户');
INSERT INTO sys_dict_data VALUES (NULL, '000000', 2, '修改', 'edit', 'sys_user_approval_type', '', 'info', 'N', 103, 1, NOW(), NULL, NULL, '修改用户');
INSERT INTO sys_dict_data VALUES (NULL, '000000', 3, '停用', 'delete', 'sys_user_approval_type', '', 'danger', 'N', 103, 1, NOW(), NULL, NULL, '停用用户');
